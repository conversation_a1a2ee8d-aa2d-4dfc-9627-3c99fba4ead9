<?php

namespace App\Http\Controllers\Reports;

use App\Helper\Helper;
use PDF;
use Carbon\Carbon;
use App\Models\FacilityBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Service;
use App\Models\FacilityUsage;
use App\Models\Facility;
use App\Models\VenueService;
use App\Models\VenueOutlets;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Exception;
use App\Models\Weekday;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\Response;

class FacilityUsageReportController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Product Sale Reports
     *
     * @param Request $request
     * @return Response
     */
    public function listFacilityUsageReport(Request $request)
    {
        try {
            $rules = [
                'mode' => 'string|required',
                'from_date' => 'date|required',
                'to_date' => 'date|required',
                'facility_ids' => 'nullable|string',
                'outlet_ids' => 'nullable|string',
                'venue_service_id' => 'nullable|integer',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }
            $facilityUsageReport = $this->getData($request);

            return response()->json(['status' => true, 'message' => 'Success', 'data' => $facilityUsageReport], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'data' => get_class($e), 'message' => $e->getMessage()], 409);
        }
    }

    /**
     * Get Data for Reports and Excel
     *
     * @param Request $request
     * @return Response
     */

    public function getData($request)
    {
        $this->updateFacilityUsageData();
        $date = Helper::getSalesConfigTime($this->venue, $request->from_date, $request->to_date);
        // Log::info("sales config >>" . json_encode($date));
        $fromDate = $date['from_date'];
        $toDate = $date['to_date'];
        $startTime = $date['start_time'];
        $endTime = $date['end_time'];

        $dateFormatter = "%Y-%m-%d";
        $selectFormatter = "%a, %D %b %Y";
        switch ($request->input("mode")) {
            case "w": {
                    $dateFormatter = "%V";
                    $selectFormatter = "%D %b %Y";
                    break;
                }
            case "m": {
                    $dateFormatter = "%Y-%m";
                    $selectFormatter = "%M %Y";
                    break;
                }
            case "y": {
                    $dateFormatter = "%Y";
                    $selectFormatter = "%Y";
                    break;
                }
            default: {
                    $dateFormatter = "%Y-%m-%d";
                    $selectFormatter = "%a, %D %b %Y";
                }
        }

        $facilitySub = DB::table('facility_bookings as fb')
            //->leftJoin('facility_booking_items as fbi', 'fb.id', 'fbi.facility_booking_id')
            ->join('facilities as f', 'f.id', 'fb.facility_id')
            ->leftJoin('venue_outlets as vo', 'vo.id', 'f.venue_outlet_id')
            ->join('facility_services as fs', 'f.id', 'fs.facility_id')
            ->join('venue_services as vs', 'fs.venue_service_id', 'vs.id')
            ->join('services as s', 'vs.service_id', 's.id')
            ->join('orders as o', 'o.id', 'fb.order_id')
            ->join('customers as cus', 'cus.id', 'o.customer_id')
            ->leftJoin('order_items as oi', 'oi.order_id', 'o.id')
            ->leftJoin('orders', function ($query) {
                $query->on('orders.parent_order_id', 'o.id')
                    ->whereIn('orders.status_id', [4, 8]);
            })
            ->leftJoin('split_orders', function ($query) {
                $query->on('split_orders.parent_order_id', 'o.id')
                    ->whereIn('split_orders.status_id', [1]);
            })
            // ->leftJoin('orders as spo', function ($query) {
            //     $query->on('spo.id', 'split_orders.order_id')
            //         ->whereIn('spo.status_id', [4, 8]);
            // })
            ->whereNotIn('fb.status_id', [2, 5])
            // ->where('fb.date', '<=', $request->to_date)
            // ->where('fb.date', '>=', $request->from_date)
            ->whereRaw("CONCAT(fb.date,' ', fb.start_time) <= CONCAT('$toDate',' ', '$endTime')")
            ->whereRaw("CONCAT(fb.date,' ', fb.start_time) >= CONCAT('$fromDate',' ', '$startTime' )")
            ->where(function ($query) {
                $query->where(function ($query) {
                    $query->whereIn('o.status_id', [4, 8, 14, 21])
                        ->WhereIn('o.order_status_id', [8, 11, 12, 13, 17, 18, 19, 20]);
                });
            })
            ->where(['f.venue_id' => $this->venueId]);

        $facilitySub1 = $facilitySub->select(
            'f.id',
            'f.name as facility',
            'f.venue_outlet_id',
            'vo.name as outlet_name',
            'cus.first_name',
            'fb.id as fb_id',
            'fb.date',
            'fb.start_time',
            'fb.end_time',
            's.name as service',
            // DB::raw("((TIME_TO_SEC(timediff(fb.end_time,fb.start_time))/60)/60) as hours"),
            DB::raw("IF(fb.duration is not null,(duration/60),
            (
           ROUND(
           (
           CASE
           WHEN fb.booking_end_date IS NOT NULL THEN
               TIME_TO_SEC(TIMEDIFF(CONCAT(fb.booking_end_date, ' ', fb.end_time), CONCAT(fb.date, ' ', fb.start_time))) / 3600
           ELSE
               TIME_TO_SEC(TIMEDIFF(fb.end_time, fb.start_time)) / 3600
           END
           ),
           3
           ))) AS hours
           "),
            DB::raw("IF(o.status_id=21,0,(o.total)) as sales"),
            'o.total as parent_order_total',
            DB::raw("IF(orders.id is not null and orders.order_status_id=8,(orders.total),0) as refund_sales"),
            'o.id as parent_order_id',
            'orders.id as child_order_id',
            DB::raw("(select count(*) from split_orders as so where so.parent_order_id = o.id) as split_count"),
        )
            //->where('oi.status_id', '!=', 2)
            ->where('f.status_id', 1)
            ->groupBy('fb.date', 'fb.facility_id', 'fb.start_time', 'fb.end_time', 'o.id', 'child_order_id');
        // Initialize the query builder
        $facilitySub1 = $facilitySub;

        // Handle facility_ids parameter - only apply if not empty
        if ($request->has('facility_ids') && $request->input('facility_ids') != '') {
            $facilityIds = explode(",", $request->input('facility_ids'));
            $facilitySub1 = $facilitySub1->whereIn('fb.facility_id', $facilityIds);
        }

        // Handle outlet_ids parameter - filter facilities by venue_outlet_id
        if ($request->has('outlet_ids') && $request->input('outlet_ids') != '') {
            $outletIds = explode(",", $request->input('outlet_ids'));
            $facilitySub1 = $facilitySub1->whereIn('f.venue_outlet_id', $outletIds);
        }

        if ($request->has('venue_service_id')) {
            $facilitySub1 = $facilitySub1->where('fs.venue_service_id', $request->input('venue_service_id'));
        }


        $facilitySub2 = DB::table(DB::raw("({$facilitySub1->toSql()}) as sub1"))->select(
            'id',
            'date',
            'service',
            'facility',
            'venue_outlet_id',
            'outlet_name',
            'hours',
            //  'sales',
            DB::raw('IF(split_count>0,((sum(sales) +parent_order_total) - sum(refund_sales)),(sum(sales)-sum(refund_sales))) as sales'),
            'refund_sales',
            'first_name',
            'start_time',
            'end_time',
            'fb_id',
            'parent_order_id',
            'split_count'
        )->mergeBindings($facilitySub1)
            ->groupBy('parent_order_id');

        $facilityTotalQuery = DB::table(DB::raw("({$facilitySub2->toSql()}) as sub"))->select(
            'id',
            'date',
            'service',
            'facility',
            'venue_outlet_id',
            'outlet_name',
            DB::raw('sum(hours) as hours'),
           DB::raw('sum(sales) as sales'),
            'first_name',
            'start_time',
            'end_time',
            'fb_id',
            'split_count'
        )
            ->mergeBindings($facilitySub);
        $facilityDetailsQuery = DB::table(DB::raw("({$facilitySub2->toSql()}) as sub"))->select(
            'id',
            'date',
            'service',
            'facility',
            'venue_outlet_id',
            'outlet_name',
            DB::raw('sum(hours) as hours'),
            DB::raw('(sum(sales)) as sales'),
            'first_name',
            'start_time',
            'end_time',
            'fb_id',
            'split_count'
        )
            ->mergeBindings($facilitySub);
        // return $facilityDetailsQuery->get();
        $data = [];
        $data['total'] = $facilityTotalQuery->groupBy('id')->get();
        $facilityDetails = $facilityDetailsQuery->groupBy('date', 'fb_id', 'start_time', 'end_time');

        // return $facilityDetailsQuery->get();
        $query = DB::table(DB::raw("(select a.selected_date,dayname(a.selected_date) as dayname, w.bit_value from
                        (select adddate('1970-01-01',t4.i*10000 + t3.i*1000 + t2.i*100 + t1.i*10 + t0.i) selected_date from
                        (select 0 i union select 1 union select 2 union select 3 union select 4 union select 5 union select 6 union select 7 union select 8 union select 9) t0,
                        (select 0 i union select 1 union select 2 union select 3 union select 4 union select 5 union select 6 union select 7 union select 8 union select 9) t1,
                        (select 0 i union select 1 union select 2 union select 3 union select 4 union select 5 union select 6 union select 7 union select 8 union select 9) t2,
                        (select 0 i union select 1 union select 2 union select 3 union select 4 union select 5 union select 6 union select 7 union select 8 union select 9) t3,
                        (select 0 i union select 1 union select 2 union select 3 union select 4 union select 5 union select 6 union select 7 union select 8 union select 9) t4) as a
                        inner join weekdays as w on w.name = dayname(a.selected_date) ) as d"))

            ->select(
                DB::raw("date_format(d.selected_date,'$selectFormatter') as date"),
                DB::raw('round(SUM(facilityDetails.hours),2) as hours'),
                DB::raw('round(SUM(facilityDetails.sales),4) as sales'),
                'facilityDetails.facility as facility',
                'facilityDetails.service as service',
                'facilityDetails.venue_outlet_id',
                'facilityDetails.outlet_name',
                'facilityDetails.first_name as name',
                DB::raw("date_format(facilityDetails.start_time, '%H:%i') as start_time"),
                DB::raw("date_format(facilityDetails.end_time, '%H:%i') as end_time"),
                'facilityDetails.id',
                'facilityDetails.fb_id',
                DB::raw("date_format(d.selected_date,'%Y-%m-%d') as row_date")
            )

            ->leftJoinSub($facilityDetails, 'facilityDetails', function ($join) {
                $join->on("d.selected_date", '=', 'facilityDetails.date');
            })
            ->where('d.selected_date', '<=', $toDate)
            ->where('d.selected_date', '>=', $fromDate)
            ->groupBy(DB::raw("date_format(d.selected_date, '$dateFormatter')"), 'facilityDetails.id',  'facilityDetails.start_time', 'facilityDetails.end_time', 'fb_id')
            ->orderBy('d.selected_date', 'DESC')->orderBy('facilityDetails.id');

        $data['data'] = $query->get();
        //Log::info('-------- Facility Usage report Weekly Data----- *****' . json_encode($data['data']));
        if ($request->mode == 'w') {
            for ($i = 0; $i < count($data['data']); $i++) {
                $startDate = Carbon::parse($data['data'][$i]->date)->startOfWeek(Carbon::SUNDAY);
                $endDate = Carbon::parse($data['data'][$i]->date)->endOfWeek(Carbon::SATURDAY);
                if ($startDate->lt(Carbon::parse($fromDate))) {
                    $startDate = Carbon::parse($fromDate);
                }
                if ($endDate->gt(Carbon::parse($toDate))) {
                    $endDate = Carbon::parse($toDate);
                }
                $data['data'][$i]->date = $startDate->format('d M Y') . " to " . $endDate->format('d M Y');
                $hoursData =  $this->listFacilityUsageDataTotal($startDate, $endDate, $data['data'][$i]->id);
                if (count($hoursData) > 0) {
                    $data['data'][$i]->inactive_hours = number_format($hoursData[0]->total_inactive_hours, 2);
                    $data['data'][$i]->active_hours = $hoursData[0]->total_active_hours;
                } else {
                    $data['data'][$i]->inactive_hours = NULL;
                    $data['data'][$i]->active_hours = NULL;
                }
            }
            $dateData = array();
            foreach ($data['data'] as $key => $value) {
                if (!isset($dateData[$value->date][$value->id])) {
                    $dateData[$value->date][$value->id] = array();
                }
                array_push($dateData[$value->date][$value->id], $value);
            }

            $i = 0;
            foreach ($dateData as $key => $value) {
                foreach ($value as $key1 => $row1) {
                    foreach ($row1 as $row2) {
                        $data['data'][$i] = $row2;
                        $i++;
                    }
                }
            }
            for ($i = 0; $i < count($data['total']); $i++) {
                $hoursData =  $this->listFacilityUsageDataTotal($fromDate, $toDate, $data['total'][$i]->id);
                if (count($hoursData) > 0) {
                    $data['total'][$i]->inactive_hours = $hoursData[0]->total_inactive_hours;
                    $data['total'][$i]->active_hours = $hoursData[0]->total_active_hours;
                } else {
                    $data['total'][$i]->inactive_hours = NULL;
                    $data['total'][$i]->active_hours = NULL;
                }
            }
        } else if ($request->mode == 'd') {
            for ($i = 0; $i < count($data['data']); $i++) {
                $hoursData =  $this->listFacilityUsageData($data['data'][$i]->row_date, $data['data'][$i]->id);
                if ($hoursData) {
                    $data['data'][$i]->inactive_hours = $hoursData->inactive_hours;
                    $data['data'][$i]->active_hours = $hoursData->active_hours;
                } else {
                    $data['data'][$i]->inactive_hours = NULL;
                    $data['data'][$i]->active_hours = NULL;
                }
            }
            for ($i = 0; $i < count($data['total']); $i++) {
                $hoursData =  $this->listFacilityUsageDataTotal($fromDate, $toDate, $data['total'][$i]->id);
                if (count($hoursData) > 0) {
                    $data['total'][$i]->inactive_hours = $hoursData[0]->total_inactive_hours;
                    $data['total'][$i]->active_hours = $hoursData[0]->total_active_hours;
                } else {
                    $data['total'][$i]->inactive_hours = NULL;
                    $data['total'][$i]->active_hours = NULL;
                }
            }
        } else {
            for ($i = 0; $i < count($data['data']); $i++) {
                if ($request->mode == 'm') {
                    $startDate = Carbon::parse($data['data'][$i]->date)->startOfMonth();
                    $endDate = Carbon::parse($data['data'][$i]->date)->endOfMonth();
                } else {
                    $startDate = Carbon::parse($data['data'][$i]->date)->startOfYear();
                    $endDate = Carbon::parse($data['data'][$i]->date)->endOfYear();
                }
                if ($startDate->lt(Carbon::parse($fromDate))) {
                    $startDate = Carbon::parse($fromDate);
                }
                if ($endDate->gt(Carbon::parse($toDate))) {
                    $endDate = Carbon::parse($toDate);
                }
                $hoursData =  $this->listFacilityUsageDataTotal($startDate, $endDate, $data['data'][$i]->id);
                if (count($hoursData) > 0) {
                    $data['data'][$i]->inactive_hours = number_format($hoursData[0]->total_inactive_hours, 2);
                    $data['data'][$i]->active_hours = $hoursData[0]->total_active_hours;
                } else {
                    $data['data'][$i]->inactive_hours = NULL;
                    $data['data'][$i]->active_hours = NULL;
                }
            }
            $dateData = array();
            foreach ($data['data'] as $key => $value) {
                if (!isset($dateData[$value->date][$value->id])) {
                    $dateData[$value->date][$value->id] = array();
                }
                array_push($dateData[$value->date][$value->id], $value);
            }

            $i = 0;
            foreach ($dateData as $key => $value) {
                foreach ($value as $key1 => $row1) {
                    foreach ($row1 as $row2) {
                        $data['data'][$i] = $row2;
                        $i++;
                    }
                }
            }
            for ($i = 0; $i < count($data['total']); $i++) {
                $hoursData =  $this->listFacilityUsageDataTotal($fromDate, $toDate, $data['total'][$i]->id);
                if (count($hoursData) > 0) {
                    $data['total'][$i]->inactive_hours = $hoursData[0]->total_inactive_hours;
                    $data['total'][$i]->active_hours = $hoursData[0]->total_active_hours;
                } else {
                    $data['total'][$i]->inactive_hours = NULL;
                    $data['total'][$i]->active_hours = NULL;
                }
            }
        }
        return $data;
    }
    /**
     * Product Sale Excel Generation
     *
     * @param Request $request
     * @return Response
     */
    public function listFacilityUsageReportExcel(Request $request)
    {
        try {
            $rules = [
                'from_date' => 'required|date_format:Y-m-d',
                'to_date' => 'required|date_format:Y-m-d|after_or_equal:from_date',
                'mode' => 'string|required',
                'facility_ids' => 'nullable|string',
                'outlet_ids' => 'nullable|string',
                'venue_service_id' => 'nullable|integer',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');
            $facilitiesString = '';
            $outletsString = '';
            $serviceName = '';

            if ($request->has('facility_ids') && $request->input('facility_ids') != '' && $request->has('facility_ids') != null) {
                $facilityIds = explode(",", $request->input('facility_ids'));
                $facilities = Facility::whereIn('id',  $facilityIds)->get();
                foreach ($facilities as $row) {
                    if ($facilitiesString == '')
                        $facilitiesString =  ' | ' . $row->name;
                    else
                        $facilitiesString .= ',' . $row->name;
                }
            }

            if ($request->has('outlet_ids') && $request->input('outlet_ids') != '' && $request->has('outlet_ids') != null) {
                $outletIds = explode(",", $request->input('outlet_ids'));
                $outlets = VenueOutlets::whereIn('id',  $outletIds)->get();
                foreach ($outlets as $row) {
                    if ($outletsString == '')
                        $outletsString =  ' | Outlets: ' . $row->name;
                    else
                        $outletsString .= ',' . $row->name;
                }
            }

            if ($request->has('venue_service_id')) {
                $venueService = VenueService::where('id', $request->input('venue_service_id'))->first();
                if ($venueService)
                    $service = Service::where('id', $venueService->service_id)->first();
                if ($service)
                    $serviceName = ' | ' . $service->name;
            }

            $title = 'Facility Usage Report Report ' . $serviceName . $facilitiesString . $outletsString . ' | From ' . Carbon::parse($fromDate)->format('j M Y') . ' To ' . Carbon::parse($toDate)->format('j M Y');

            $facilityDetails = $this->getData($request);
            $venueName = $this->venue->name;
            // Generate Excel Report from Template
            $spreadsheet = IOFactory::load('../resources/views/export/facility-usage-template.xlsx');
            $worksheet = $spreadsheet->getActiveSheet();
            $this->addLogoToExcel($spreadsheet);
            $worksheet->getCell('B1')->setValue($venueName);
            $worksheet->getCell('B2')->setValue($title);
            $row = 3;
            $date = "";
            $spreadsheet->getActiveSheet()->getStyle('A3:G3')->applyFromArray(
                array(
                    'fill' => array(
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'color' => array('argb' => 'FF066A8C')
                    ),
                    'font'  => array(
                        'bold'  => true,
                        'color' => array('argb' => 'FFFFFFFF'),
                    )
                )
            );

            $spreadsheet->getActiveSheet()->getColumnDimension('B')->setWidth(240, 'pt');

            $dataAvailabilityFlag = false;
            $serviceName = "";
            $currentFacilityName = "";
            $totalDuration = $totalRevenue = 0;
            $totalInactive = 0;
            $dataFlag = false;
            $j = 0;
            foreach ($facilityDetails['data'] as $facilityUsage) {
                if ($facilityUsage->facility != null) {
                    $dataAvailabilityFlag = true;
                    $currentData[$j] = $facilityUsage;
                    if ($facilityUsage->date != $date) {
                        $row++;
                        if ($row > 4) {
                            $worksheet->getCell('A' . $row)->setValue('Total Hours');
                            $worksheet->getCell('F' . $row)->setValue("Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)");
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->getFont()->setBold(true);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->applyFromArray(
                                array(
                                    'fill' => array(
                                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                        'color' => array('argb' => 'FFEFF9F9')
                                    )
                                )
                            );
                            $row++;
                            $worksheet->getCell('A' . $row)->setValue('Total Revenue');
                            $worksheet->getCell('H' . $row)->setValue(isset($dayRevenue) ?  $this->format_amount($dayRevenue, 4) : 0);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->getFont()->setBold(true);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->applyFromArray(
                                array(
                                    'fill' => array(
                                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                        'color' => array('argb' => 'FFEFF9F9')
                                    )
                                )
                            );
                            $row++;
                        }
                        $serviceName = "";
                        $currentFacilityName = "";
                        $dataFlag = false;
                        $dayDuration = $dayRevenue = 0;
                        $date = $facilityUsage->date;

                        $worksheet->getCell('A' . $row)->setValue($date);

                        $currentFacilityName = $facilityUsage->facility;
                        $worksheet->getCell('B' . $row)->setValue($currentFacilityName);
                    } else if ($facilityUsage->facility != $currentFacilityName) {
                        $row++;
                        if ($row > 4) {
                            $worksheet->getCell('B' . $row)->setValue('Total Hours');
                            $worksheet->getCell('F' . $row)->setValue("Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)");
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->getFont()->setBold(true);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->applyFromArray(
                                array(
                                    'fill' => array(
                                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                        'color' => array('argb' => 'FFEFF9F9')
                                    )
                                )
                            );
                            $row++;
                            $worksheet->getCell('A' . $row)->setValue('Total Revenue');
                            $worksheet->getCell('H' . $row)->setValue(isset($dayRevenue) ?  $this->format_amount($dayRevenue, 4) : 0);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->getFont()->setBold(true);
                            $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->applyFromArray(
                                array(
                                    'fill' => array(
                                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                        'color' => array('argb' => 'FFEFF9F9')
                                    )
                                )
                            );
                            $row++;
                        }
                        $serviceName = "";
                        $dataFlag = false;
                        $dayDuration = $dayRevenue = 0;
                        $currentFacilityName = $facilityUsage->facility;
                        $worksheet->getCell('B' . $row)->setValue($currentFacilityName);
                    } else {
                        $dataFlag = true;
                    }

                    if ($facilityUsage->facility != null || $dataFlag == false) {
                        $row++;
                        if ($serviceName != $facilityUsage->service) {
                            $serviceName = $facilityUsage->service;
                        }
                        $worksheet->getCell('C' . $row)->setValue(isset($facilityUsage->first_name) && $facilityUsage->first_name != null ? $facilityUsage->first_name : "NA");
                        $worksheet->getCell('D' . $row)->setValue(isset($facilityUsage->last_name) && $facilityUsage->last_name != null ? $facilityUsage->last_name : "");
                        $worksheet->getCell('E' . $row)->setValue(isset($facilityUsage->start_time) && $facilityUsage->start_time != null ? $facilityUsage->start_time : "NA");
                        $worksheet->getCell('F' . $row)->setValue(isset($facilityUsage->end_time) && $facilityUsage->end_time != null ? $facilityUsage->end_time : "NA");
                        $worksheet->getCell('G' . $row)->setValue($facilityUsage->hours . " hr(s)");
                        $worksheet->getCell('H' . $row)->setValue($this->format_amount($facilityUsage->sales, 4));
                        $totalDuration += $facilityUsage->hours;
                        $dayDuration += $facilityUsage->hours;
                        $totalRevenue += $facilityUsage->sales;
                        $dayRevenue += $facilityUsage->sales;
                    }
                    $j++;
                }
            }
            if ($dataAvailabilityFlag == true) {
                $row++;
                $worksheet->getCell('B' . $row)->setValue('Total Hours');
                $worksheet->getCell('F' . $row)->setValue("Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)");
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->getFont()->setBold(true);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->applyFromArray(
                    array(
                        'fill' => array(
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'color' => array('argb' => 'FFEFF9F9')
                        )
                    )
                );
                $row++;
                $worksheet->getCell('B' . $row)->setValue('Total Revenue');
                $worksheet->getCell('H' . $row)->setValue(isset($dayRevenue) ?  $this->format_amount($dayRevenue, 4) : 0);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->getFont()->setBold(true);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->applyFromArray(
                    array(
                        'fill' => array(
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'color' => array('argb' => 'FFEFF9F9')
                        )
                    )
                );
                $row++;
                $serviceName = "";
                $dateRange = Carbon::parse($request->input('from_date'))->format('d M Y') . " to " . Carbon::parse($request->input('to_date'))->format('d M Y');

                $worksheet->getCell('A' . $row)->setValue($dateRange);

                $total_inactive_hours = 0;
                $total_active_hours = 0;
                foreach ($facilityDetails['total'] as $facilityUsage) {
                    if ($facilityUsage->facility != null) {
                        $row++;
                        if ($serviceName != $facilityUsage->service) {
                            $serviceName = $facilityUsage->service;
                            $worksheet->getCell('B' . $row)->setValue(isset($facilityUsage->service) && $facilityUsage->service != null ? $facilityUsage->service : "NA");
                        }
                        $worksheet->getCell('C' . $row)->setValue(isset($facilityUsage->facility) && $facilityUsage->facility != null ? $facilityUsage->facility : "NA");
                        $worksheet->getCell('G' . $row)->setValue("Active: " . $facilityUsage->active_hours . " hr(s) Inactive: " . $facilityUsage->inactive_hours . " hr(s)");
                        $worksheet->getCell('H' . $row)->setValue($this->format_amount($facilityUsage->sales, 4));
                        $total_active_hours += $facilityUsage->active_hours;
                        $total_inactive_hours += $facilityUsage->inactive_hours;
                    }
                }
                $row++;
                $worksheet->getCell('B' . $row)->setValue('GRAND TOTAL - Hours');
                $worksheet->getCell('F' . $row)->setValue($totalDuration . " hr(s)");
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->getFont()->setBold(true);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":G" . $row)->applyFromArray(
                    array(
                        'fill' => array(
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'color' => array('argb' => 'FFEFF9F9')
                        )
                    )
                );
                $row++;
                $worksheet->getCell('B' . $row)->setValue('GRAND TOTAL - Revenue');
                $worksheet->getCell('H' . $row)->setValue(isset($totalRevenue) ?  $this->format_amount($totalRevenue, 4) : 0);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->getFont()->setBold(true);
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ":H" . $row)->applyFromArray(
                    array(
                        'fill' => array(
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'color' => array('argb' => 'FFEFF9F9')
                        )
                    )
                );
            } else {
                $row++;
                $worksheet->mergeCells('A' . $row . ':G' . $row);
                $worksheet->getCell('A' . $row)->setValue("No data found!");
            }

            $worksheet->getStyle('A1:G' . ($row))->applyFromArray($this->getExcelBorderStyle());
            // Save file
            $filename = '../resources/views/list-facility-usage-report' . time() . '.xlsx';
            $writer = new Xlsx($spreadsheet);
            $writer->save($filename);

            return response()->download($filename)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' =>  $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function listFacilityUsageReportPdf(Request $request)
    {
        try {
            $rules = [
                'mode' => 'string|required',
                'from_date' => 'required|date_format:Y-m-d',
                'to_date' => 'required|date_format:Y-m-d|after_or_equal:from_date',
                'facility_ids' => 'nullable|string',
                'outlet_ids' => 'nullable|string',
                'venue_service_id' => 'nullable|integer',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');
            $facilitiesString = '';
            $outletsString = '';
            $serviceName = '';

            if ($request->has('facility_ids') && $request->input('facility_ids') != '' && $request->has('facility_ids') != null) {
                $facilityIds = explode(",", $request->input('facility_ids'));
                $facilities = Facility::whereIn('id',  $facilityIds)->get();
                foreach ($facilities as $row) {
                    if ($facilitiesString == '')
                        $facilitiesString =  ' | ' . $row->name;
                    else
                        $facilitiesString .= ',' . $row->name;
                }
            }

            if ($request->has('outlet_ids') && $request->input('outlet_ids') != '' && $request->has('outlet_ids') != null) {
                $outletIds = explode(",", $request->input('outlet_ids'));
                $outlets = VenueOutlets::whereIn('id',  $outletIds)->get();
                foreach ($outlets as $row) {
                    if ($outletsString == '')
                        $outletsString =  ' | Outlets: ' . $row->name;
                    else
                        $outletsString .= ',' . $row->name;
                }
            }

            if ($request->has('venue_service_id')) {
                $venueService = VenueService::where('id', $request->input('venue_service_id'))->first();
                if ($venueService)
                    $service = Service::where('id', $venueService->service_id)->first();
                if ($service)
                    $serviceName = ' | ' . $service->name;
            }

            $title = 'Facility Usage Report Report ' . $serviceName . $facilitiesString . $outletsString . ' | From ' . Carbon::parse($fromDate)->format('j M Y') . ' To ' . Carbon::parse($toDate)->format('j M Y');

            $facilityDetails = $this->getData($request);
            $fields = ["Timestamp", "Customer Name", "Start Time", "End Time", "Hours", "Revenue"];
            $data = [];
            $i = 0;
            $html = "";
            $date = "";
            $dataFlag = "";
            $dataAvailabilityFlag = false;
            $currentFacilityName = "";
            $serviceName = "";
            $totalDuration = $totalRevenue = 0;
            $totalInactive = 0;
            $dayDuration = $dayRevenue = 0;
            $currentData = array();
            $j = 0;
            foreach ($facilityDetails['data'] as $facilityUsage) {
                if ($facilityUsage->facility != null) {
                    $currentData[$j] = $facilityUsage;
                    $dataAvailabilityFlag = true;
                    if ($facilityUsage->date != $date) {
                        if ($i > 0) {
                            $html .= "<tr class='highlight'>";
                            $html .= "<td colspan='4'>Total Hours</td><td>Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)</td>";
                            $html .= "<td></td>";
                            $html .= "</tr>";
                            $html .= "<tr class='highlight'>";
                            $html .= "<td colspan='5'>Total Revenue</td><td></td>";
                            $html .= "<td>" . $this->format_amount($dayRevenue, 4) . "</td>";
                            $html .= "</tr>";
                        }
                        $dayDuration = $dayRevenue = 0;
                        $serviceName = "";
                        $currentFacilityName = "";
                        $dataFlag = false;
                        $date = $facilityUsage->date;

                        $html .= "<tr>";
                        $currentFacilityName = $facilityUsage->facility;
                        $html .= "<td  colspan='6' class='head-highlight'>" . $currentFacilityName . "</td></tr>";
                    } else  if ($facilityUsage->facility != $currentFacilityName) {
                        if ($i > 0) {
                            $html .= "<tr class='highlight'>";
                            $html .= "<td colspan='4'>Total Hours</td><td>Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)</td>";
                            $html .= "<td></td>";
                            $html .= "</tr>";
                            $html .= "<tr class='highlight'>";
                            $html .= "<td colspan='5'>Total Revenue</td><td></td>";
                            $html .= "<td>" . $this->format_amount($dayRevenue, 4) . "</td>";
                            $html .= "</tr>";
                        }
                        $dayDuration = $dayRevenue = 0;
                        $serviceName = "";
                        $currentFacilityName = "";
                        $dataFlag = false;

                        $html .= "<tr>";
                        $currentFacilityName = $facilityUsage->facility;
                        $html .= "<td  colspan='6' class='head-highlight'>" . $currentFacilityName . "</td></tr>";
                    } else {
                        $dataFlag = true;
                    }
                    if ($facilityUsage->facility != null || $dataFlag == false) {
                        $html .= "<tr>";
                        $html .= "<td>" . $date . "</td>";
                        if ($serviceName != $facilityUsage->service) {
                            $serviceName = $facilityUsage->service;
                        }
                        $html .= "<td>" . (isset($facilityUsage->name) ? $facilityUsage->name : "NA") . "</td>";
                        $html .= "<td>" . (isset($facilityUsage->start_time) ? $facilityUsage->start_time : "NA") . "</td>";
                        $html .= "<td>" . (isset($facilityUsage->end_time) ? $facilityUsage->end_time : "NA") . "</td>";
                        $html .= "<td>" . $facilityUsage->hours . " hr(s)</td>";
                        $html .= "<td>" . $this->format_amount($facilityUsage->sales, 4) . "</td></tr>";
                        $totalDuration += $facilityUsage->hours;
                        $dayDuration += $facilityUsage->hours;
                        $totalRevenue += $facilityUsage->sales;
                        $dayRevenue += $facilityUsage->sales;
                    }
                    $i++;
                    $j++;
                }
            }
            if ($dataAvailabilityFlag == true) {
                $html .= "<tr class='highlight'>";
                $html .= "<td colspan='4'>Total Hours</td><td>Active: " . $currentData[$j - 1]->active_hours . " hr(s)  Inactive: " . $currentData[$j - 1]->inactive_hours . " hr(s)</td>";
                $html .= "<td></td>";
                $html .= "</tr>";
                $html .= "<tr class='highlight'>";
                $html .= "<td colspan='5'>Total Revenue</td><td></td>";
                $html .= "<td>" . $this->format_amount($dayRevenue, 2) . "</td>";
                $html .= "</tr>";
                $serviceName = "";
                $dateRange = Carbon::parse($request->input('from_date'))->format('d M Y') . " to " . Carbon::parse($request->input('to_date'))->format('d M Y');
                $html .= "<tr class='date-highlight'><td  colspan='6'>" . $dateRange . "</td></tr>";
                $total_inactive_hours = 0;
                $total_active_hours = 0;
                foreach ($facilityDetails['total'] as $facilityUsage) {
                    if ($facilityUsage->facility != null) {
                        $html .= "<tr>";
                        $html .= "<td></td>";
                        if ($serviceName != $facilityUsage->service) {
                            $serviceName = $facilityUsage->service;
                            $html .= "<td>" . (isset($facilityUsage->service) ? $facilityUsage->service : "NA") . "</td>";
                        } else {
                            $html .= "<td></td>";
                        }
                        $html .= "<td colspan='2'>" . (isset($facilityUsage->facility) ? $facilityUsage->facility : "NA") . "</td>";
                        $html .= "<td> Active: " . $facilityUsage->active_hours . " hr(s)
                        Inactive: " . $facilityUsage->inactive_hours . " hr(s)</td>";
                        $html .= "<td>" . $facilityUsage->sales . "</td></tr>";
                        $total_active_hours += $facilityUsage->active_hours;
                        $total_inactive_hours += $facilityUsage->inactive_hours;
                    }
                }
                $html .= "<tr class='highlight'>";
                $html .= "<td colspan='4'>GRAND TOTAL - Hours</td><td>Active: " . $total_active_hours . " hr(s)  Inactive: " . $total_inactive_hours . " hr(s)</td>";
                $html .= "<td></td>";
                $html .= "</tr>";
                $html .= "<tr class='highlight'>";
                $html .= "<td colspan='5'>GRAND TOTAL - Revenue</td><td></td>";
                $html .= "<td>" . $this->format_amount($totalRevenue, 4) . "</td>";
                $html .= "</tr>";
            } else {
                $html .= "<tr><td  colspan='6'><strong>No data found!</strong></td></tr>";
            }
            $data['venueName'] = $this->venue->name;
            $data['reportName'] = $title;
            $data['venueLogo'] = $this->venue->profile_image != null ? config('filesystems.disks.azure.account_url') . '/' . $this->venue->profile_image :
                base_path() . '/resources/views/export/logo.png';

            $pdf = PDF::loadView('export/pdf_template', ['data' => $data, 'fields' => $fields, 'dataHtml' => $html]);

            return $pdf->download('list-discount-sales-report.pdf');
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function listFacilityUsageData($date, $facilityId)
    {
        try {
            $facilityUsage = FacilityUsage::where(['facility_id' => $facilityId, 'date' => $date])->first();
            return $facilityUsage;
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'data' => get_class($e), 'message' => $e->getMessage()], 409);
        }
    }

    public function listFacilityUsageDataTotal($fromDate, $toDate, $facilityId)
    {
        try {
            $facilityUsage = FacilityUsage::where(['facility_usages.facility_id' => $facilityId])
                ->groupBy('facility_usages.facility_id')
                ->select(
                    DB::raw("SUM(facility_usages.active_hours) as total_active_hours"),
                    DB::raw("SUM(facility_usages.inactive_hours) as total_inactive_hours"),
                    'facility_usages.*'
                )
                ->where('facility_usages.date', '<=', $toDate)
                ->where('facility_usages.date', '>=', $fromDate);

            $facilityUsage = $facilityUsage->get();

            return $facilityUsage;
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'data' => get_class($e), 'message' => $e->getMessage()], 409);
        }
    }
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function updateFacilityUsageData()
    {
        try {
            $date = Carbon::now()->format('Y-m-d');
            // $date = '2022-07-27';
            //$date =  date('Y-m-d', strtotime($date . ' + 1 days'));
            $dayOfWeek = Carbon::parse($date)->englishDayOfWeek;
            $weekdays = Weekday::all();
            $bitValue = $weekdays->firstWhere('name', $dayOfWeek)->bit_value;
            $facilities = Facility::where(['facilities.status_id' => 1])
                ->with([
                    'facilityRentals' => function ($query) use ($bitValue) {
                        $query->select(
                            'facility_rentals.start_time',
                            'facility_rentals.end_time',
                            'facility_rentals.id',
                            'facility_rentals.facility_id',
                            'facility_rentals.timing_template_id'
                        )
                            ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0");
                    }
                ])
                // ->join('facility_rentals as fr', 'fr.facility_id', 'facilities.id')
                ->orderBy('facilities.name', 'asc')->get();

            foreach ($facilities as $row) {
                if (count($row->facilityRentals) > 0) {
                    $diff_in_minutes = 0;
                    foreach ($row->facilityRentals as $rentals) {
                        $endTime = Carbon::parse($rentals->end_time);
                        $startTime = Carbon::parse($rentals->start_time);
                        $diff_in_minutes += (($endTime->diffInSeconds($startTime)) / 60);
                    }
                    $totalHours = $diff_in_minutes / 60;
                    $facilityBookings = FacilityBooking::where(['date' => $date, 'facility_id' => $row->id])
                        ->whereIn('status_id', [1, 4])
                        ->groupBy('facility_bookings.date', 'facility_bookings.facility_id', 'facility_bookings.start_time', 'facility_bookings.end_time')->get();
                    $slot_diff_in_minutes = 0;
                    foreach ($facilityBookings as $bookings) {
                        if ($bookings->booking_end_date == null) {
                            $bookingEndTime = Carbon::parse($bookings->end_time);
                            $bookingStartTime = Carbon::parse($bookings->start_time);
                        } else {
                            $bookingEndTime = Carbon::parse($bookings->booking_end_date . ' ' . $bookings->end_time);
                            $bookingStartTime = Carbon::parse($bookings->date . ' ' . $bookings->start_time);
                        }
                        if ($bookings->duration == null) {
                            $timeDiff = $bookingEndTime->diffInSeconds($bookingStartTime);
                            $slot_diff_in_minutes = $slot_diff_in_minutes + ($timeDiff / 60);
                        } else {
                            $slot_diff_in_minutes += $bookings->duration;
                        }
                    }
                    $activeHours = $slot_diff_in_minutes / 60;
                    $inactiveHours = $totalHours - $activeHours;
                    $facilityUsageExist = FacilityUsage::where(['date' => $date, 'facility_id' => $row->id])->first();
                    if (!$facilityUsageExist) {
                        $facilityUsage = new FacilityUsage;
                        $facilityUsage->date = $date;
                        $facilityUsage->facility_id = $row->id;
                        $facilityUsage->inactive_hours = $inactiveHours;
                        $facilityUsage->active_hours = $activeHours;
                        $facilityUsage->total_hours = $totalHours;
                        $facilityUsage->save();
                    } else {
                        $facilityDataUpdate['inactive_hours'] = $inactiveHours;
                        $facilityDataUpdate['active_hours'] = $activeHours;
                        $facilityDataUpdate['total_hours'] = $totalHours;
                        FacilityUsage::where(['date' => $date, 'facility_id' => $row->id])->update($facilityDataUpdate);
                    }
                }
            }
        } catch (\Exception $e) {
            $err_subject = "Error in Facility Usage job.";
            $err_message = "Error: " . $e->getMessage() . "<br />" . $e->getFile() . "<br />" . $e->getLine() . "<br />" . $e->getMessage();
            Log::info("Error >>" . $err_message);
        }
    }
}
